'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X, Loader, Plus, Search, Filter } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { appTheme } from '@/app/theme';
import useUserStore from '@/store/userStore';
import { toast } from 'react-hot-toast';

// Types
interface User {
  id: number;
  name: string;
  email: string;
  imageUrl?: string;
  departmentName?: string;
  organizationName?: string;
  isLeader?: boolean;
  isAdmin?: boolean;
  isOwner?: boolean;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  organizationId: number;
}

interface TaskAssignment {
  userId: string;
  isLeader: boolean;
}

// ApiMember interface removed - now using the new task assignment users API

interface CreateTaskFromMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  messageContent: string;
  onTaskCreated?: (task: any) => void;
}

// Styled components
const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: ${props => (props.$isOpen ? 'flex' : 'none')};
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: ${props => props.$isOpen ? 'fadeIn 0.2s ease-out' : 'none'};

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: ${appTheme.shadows.lg};
  animation: slideIn 0.3s ease-out;

  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  
  &:hover {
    background-color: ${appTheme.colors.background};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
`;

const FormContainer = styled.form`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${appTheme.spacing.lg};

  @media (max-width: ${appTheme.breakpoints.md}) {
    grid-template-columns: 1fr;
    gap: ${appTheme.spacing.md};
  }
`;

const FormGroup = styled.div<{ $fullWidth?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  margin-bottom: 0.5rem;

  ${props =>
    props.$fullWidth &&
    `
    grid-column: 1 / -1;
  `}
`;

const FormLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
  display: block;
`;

const FormInput = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.md};
  font-size: 0.875rem;
  background-color: white;
  color: #111827;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;

  &::placeholder {
    color: #9ca3af;
  }

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: white;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    border-color: #e5e7eb;
    background-color: #f9fafb;
    color: #9ca3af;
  }
`;

const FormSelect = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.md};
  font-size: 0.875rem;
  background-color: white;
  color: #111827;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: white;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    border-color: #e5e7eb;
    background-color: #f9fafb;
    color: #9ca3af;
  }

  option {
    padding: 0.5rem;
    color: #111827;
    background-color: white;
  }
`;

const MessagePreview = styled.div`
  background-color: ${appTheme.colors.background.light};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.md};
`;

const MessagePreviewLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  display: block;
`;

const MessagePreviewContent = styled.div`
  font-size: 0.875rem;
  color: ${appTheme.colors.text.secondary};
  line-height: 1.5;
  max-height: 100px;
  overflow-y: auto;
`;

const FormActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
  padding-top: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
  grid-column: 1 / -1;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;

  &:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.3s, opacity 0.5s;
  }

  &:active:after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
  }

  ${props =>
    props.$variant === 'primary'
      ? `
    background-color: ${appTheme.colors.primary};
    color: white;

    &:hover:not(:disabled) {
      background-color: ${appTheme.colors.primaryHover};
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      transform: translateY(1px);
      box-shadow: none;
    }
  `
      : `
    background-color: transparent;
    color: ${appTheme.colors.text.secondary};
    border-color: ${appTheme.colors.border};

    &:hover:not(:disabled) {
      background-color: ${appTheme.colors.background};
      color: ${appTheme.colors.text.primary};
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:focus {
    outline: 2px solid ${props => props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.border};
    outline-offset: 2px;
  }
`;

const ErrorMessage = styled.div`
  color: ${appTheme.colors.error.main};
  font-size: ${appTheme.typography.fontSizes.sm};
  padding: ${appTheme.spacing.sm};
  background-color: ${appTheme.colors.error.light};
  border: 1px solid ${appTheme.colors.error.main};
  border-radius: ${appTheme.borderRadius.md};
  grid-column: 1 / -1;
`;

const LoadingSpinner = styled(Loader)`
  animation: spin 1s linear infinite;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

// User list styled components
const UserListContainer = styled.div`
  border: 1px solid rgba(209, 213, 219, 0.6);
  border-radius: ${appTheme.borderRadius.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  backdrop-filter: blur(10px);
  min-height: 80px;
  max-height: 300px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const UserListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.8), rgba(243, 244, 246, 0.6));
  backdrop-filter: blur(5px);
  border-radius: ${appTheme.borderRadius.lg} ${appTheme.borderRadius.lg} 0 0;
`;

const UserListTitle = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const AddUserButton = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  user-select: none;

  &:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`;

const UserListContent = styled.div`
  padding: ${appTheme.spacing.sm};
  overflow-y: auto;
  flex: 1;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;

    &:hover {
      background: rgba(156, 163, 175, 0.7);
    }
  }
`;

const UserItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  margin-bottom: ${appTheme.spacing.xs};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.9));
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  gap: ${appTheme.spacing.sm};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease-in-out;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(99, 102, 241, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));

    &::before {
      left: 100%;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string; $size?: 'small' | 'medium' }>`
  width: ${props => (props.$size === 'small' ? '32px' : '36px')};
  height: ${props => (props.$size === 'small' ? '32px' : '36px')};
  border-radius: 50%;
  background: ${props =>
    props.$imageUrl ? `url(${props.$imageUrl})` : 'linear-gradient(135deg, #6366f1, #8b5cf6)'};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${appTheme.typography.fontWeights.semibold};
  font-size: ${props =>
    props.$size === 'small' ? appTheme.typography.fontSizes.xs : appTheme.typography.fontSizes.sm};
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
`;

const UserName = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: #1f2937;
  letter-spacing: -0.025em;
`;

const UserEmail = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #6b7280;
  font-weight: 400;
`;

const UserDepartmentOrg = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #9ca3af;
  font-weight: 400;
  font-style: italic;
`;

const LeaderBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: #fbbf24;
  color: #92400e;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  border-radius: 4px;
  margin-left: 6px;
`;

const AdminBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: #3b82f6;
  color: white;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  border-radius: 4px;
  margin-left: 6px;
`;

const OwnerBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: #dc2626;
  color: white;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  border-radius: 4px;
  margin-left: 6px;
`;

const UserActions = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
`;

const LeaderCheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  cursor: pointer;
  user-select: none;
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  transition: all 0.2s ease-in-out;
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.1);

  &:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
  }
`;

const LeaderText = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: #4f46e5;
  letter-spacing: 0.025em;
`;

const RemoveUserButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #ef4444;
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.md};
  transition: all 0.2s ease-in-out;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const EmptyUserList = styled.div`
  text-align: center;
  padding: ${appTheme.spacing.md};
  color: #9ca3af;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-style: italic;
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.5), rgba(243, 244, 246, 0.3));
  border-radius: ${appTheme.borderRadius.md};
  margin: ${appTheme.spacing.xs};
  border: 1px dashed rgba(156, 163, 175, 0.3);
`;

// Modal styled components for user search
const SearchModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
`;

const SearchModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.xl};
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: ${appTheme.shadows.lg};
`;

const SearchModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${appTheme.spacing.lg};
`;

const SearchModalTitle = styled.h2`
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.bold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const SearchCloseButton = styled.button`
  background: none;
  border: none;
  font-size: ${appTheme.typography.fontSizes.xl};
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    background-color: #f3f4f6;
    color: ${appTheme.colors.text.primary};
  }
`;

// Custom Checkbox Component
const CheckboxContainer = styled.div`
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;

  &:hover .checkbox-box {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: scale(1.05);
  }
`;

const CheckboxInput = styled.input`
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;

  &:disabled + .checkbox-box {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f3f4f6;
    border-color: #d1d5db;
  }

  &:disabled ~ .checkbox-container {
    cursor: not-allowed;
  }
`;

const CheckboxBox = styled.div<{ $checked: boolean; $disabled: boolean }>`
  position: relative;
  width: 20px;
  height: 20px;
  border: 2px solid ${props => (props.$checked ? '#6366f1' : '#d1d5db')};
  border-radius: 6px;
  background: ${props =>
    props.$checked
      ? 'linear-gradient(135deg, #6366f1, #4f46e5)'
      : 'linear-gradient(135deg, #ffffff, #f8fafc)'};
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${props =>
    props.$checked ? '0 2px 8px rgba(99, 102, 241, 0.3)' : '0 1px 3px rgba(0, 0, 0, 0.1)'};

  ${props =>
    props.$disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
    background: #f3f4f6;
    border-color: #d1d5db;
    box-shadow: none;
  `}

  &::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2.5px 2.5px 0;
    transform: rotate(45deg) scale(${props => (props.$checked ? '1' : '0')});
    transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    top: 2px;
    left: 6px;
  }
`;

interface CustomCheckboxProps {
  checked: boolean;
  onChange: () => void;
  disabled?: boolean;
  className?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  checked,
  onChange,
  disabled = false,
  className,
}) => {
  return (
    <CheckboxContainer
      className={`checkbox-container ${className || ''}`}
      onClick={disabled ? undefined : onChange}
    >
      <CheckboxInput type="checkbox" checked={checked} onChange={onChange} disabled={disabled} />
      <CheckboxBox className="checkbox-box" $checked={checked} $disabled={disabled} />
    </CheckboxContainer>
  );
};

// Helper function to get user initials
const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

export default function CreateTaskFromMessageModal({
  isOpen,
  onClose,
  messageContent,
  onTaskCreated,
}: CreateTaskFromMessageModalProps) {
  const { getToken } = useAuth();
  const { userData } = useUserStore();

  const [formData, setFormData] = useState({
    taskTitle: '',
    organizationId: '',
    departmentId: '',
    points: '',
    dueDate: '',
  });

  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [assignedUsers, setAssignedUsers] = useState<User[]>([]);
  const [assignedToUserIds, setAssignedToUserIds] = useState<string[]>([]);
  const [taskAssignments, setTaskAssignments] = useState<TaskAssignment[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  // Modal state for user search
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchFilters, setSearchFilters] = useState({
    organizationId: '',
    departmentId: '',
    name: '',
  });
  const [modalOrganizations, setModalOrganizations] = useState<Organization[]>([]);
  const [modalDepartments, setModalDepartments] = useState<Department[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Load organizations on mount and set up initial data
  useEffect(() => {
    if (isOpen && userData) {
      // Handle organizations
      const orgs = userData.organizations || [];
      setOrganizations(orgs);

      // Auto-select first organization if available
      if (orgs.length > 0) {
        const firstOrg = orgs[0];
        if (firstOrg && typeof firstOrg.id !== 'undefined') {
          setFormData(prev => ({
            ...prev,
            organizationId: String(firstOrg.id),
          }));
        }
      }

      // Initialize empty assigned users list
      setAssignedUsers([]);
      setAssignedToUserIds([]);
      setTaskAssignments([]);
    }
  }, [isOpen, userData]);

  // Load departments when organization changes
  useEffect(() => {
    if (formData.organizationId) {
      fetchDepartments(Number(formData.organizationId));
    } else {
      setDepartments([]);
      setFormData(prev => ({ ...prev, departmentId: '' }));
    }
  }, [formData.organizationId]);

  const fetchDepartments = async (organizationId: number) => {
    try {
      const token = await getToken();
      const response = await fetch(`/api/v1/department?organizationId=${organizationId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setDepartments(data.departments || []);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Remove user from assigned list
  const removeUserFromList = (userId: number) => {
    setAssignedUsers(prev => prev.filter(u => u.id !== userId));
    setAssignedToUserIds(prev => prev.filter(id => id !== userId.toString()));
    setTaskAssignments(prev => prev.filter(assignment => assignment.userId !== userId.toString()));
  };

  // Toggle leader status for a user
  const toggleLeaderStatus = (userId: string) => {
    setTaskAssignments(prev =>
      prev.map(assignment =>
        assignment.userId === userId
          ? { ...assignment, isLeader: !assignment.isLeader }
          : assignment
      )
    );
  };

  // Open modal for user search
  const openModal = async () => {
    setIsModalOpen(true);

    // Use organizations from userData.organizations instead of API
    if (modalOrganizations.length === 0 && userData?.organizations) {
      try {
        // Map userData.organizations to the format expected by modal
        const orgs = userData.organizations.map(org => ({
          id: org.id,
          name: org.name,
        }));
        setModalOrganizations(orgs);

        // If there are organizations, set the first one as default and trigger search
        if (orgs.length > 0) {
          const firstOrgId = orgs[0].id.toString();
          setSearchFilters(prev => ({ ...prev, organizationId: firstOrgId }));

          // Fetch departments for the first organization
          const token = await getToken();
          if (token) {
            const deptResponse = await fetch(`/api/v1/department?organizationId=${firstOrgId}`, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (deptResponse.ok) {
              const deptData = await deptResponse.json();
              setModalDepartments(deptData.departments || []);
            }
          }

          // Trigger initial search with first organization
          searchUsers({ organizationId: firstOrgId, departmentId: '', name: '' });
        }
      } catch (err) {
        console.error('Error setting up organizations for modal:', err);
      }
    } else if (modalOrganizations.length > 0) {
      // If organizations are already loaded, trigger search with current filters or first org
      const currentOrg = searchFilters.organizationId || modalOrganizations[0]?.id.toString();
      if (currentOrg) {
        searchUsers({ ...searchFilters, organizationId: currentOrg });
      }
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSearchResults([]);
    setSearchFilters({ organizationId: '', departmentId: '', name: '' });
    setModalDepartments([]);
    setSelectedUsers(new Set());
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      setSearchTimeout(null);
    }
  };

  // Search for users
  const searchUsers = async (filters = searchFilters) => {
    if (!filters.organizationId && !filters.departmentId && !filters.name.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const token = await getToken();
      if (!token) return;

      let url = '/api/v1/task-assignment-users?';
      const params = new URLSearchParams();

      // Always include organizationId for the new endpoint
      if (filters.organizationId) {
        params.append('organizationId', filters.organizationId);
      }

      if (filters.departmentId) {
        params.append('departmentId', filters.departmentId);
      }

      if (filters.name.trim()) {
        params.append('name', filters.name.trim());
      }

      const response = await fetch(url + params.toString(), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const apiMembers = data.members || [];

        // Transform API response to match our User interface
        const transformedMembers: User[] = apiMembers.map((member: any) => ({
          id: member.user.id,
          name: `${member.user.firstName} ${member.user.lastName}`,
          email: member.user.email,
          imageUrl: member.user.imageUrl,
          departmentName: member.department?.name,
          organizationName: member.department?.organization?.name,
          isLeader: member.isLeader,
          isAdmin: member.isAdmin,
          isOwner: member.isOwner,
        }));

        setSearchResults(transformedMembers);
      }
    } catch (err) {
      console.error('Error searching users:', err);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounced search function
  const debouncedSearch = (filters: typeof searchFilters) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      searchUsers(filters);
    }, 500); // 500ms debounce

    setSearchTimeout(timeout);
  };

  // Handle search filter changes
  const handleSearchFilterChange = async (field: string, value: string) => {
    const newFilters = { ...searchFilters, [field]: value };
    setSearchFilters(newFilters);

    // If organization changed, fetch departments for that organization
    if (field === 'organizationId' && value) {
      try {
        const token = await getToken();
        if (!token) return;

        const response = await fetch(`/api/v1/department?organizationId=${value}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setModalDepartments(data.departments || []);
        }
        
      } catch (err) {
        console.error('Error fetching departments for modal:', err);
        setModalDepartments([]);
      }
    } else if (field === 'organizationId' && !value) {
      setModalDepartments([]);
      newFilters.departmentId = '';
    }

    // Trigger debounced search if we have search criteria
    if (newFilters.organizationId || newFilters.departmentId || newFilters.name.trim()) {
      debouncedSearch(newFilters);
    } else {
      setSearchResults([]);
    }
  };

  // Toggle user selection in modal
  const toggleUserSelection = (user: User) => {
    setSelectedUsers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(user.id)) {
        newSet.delete(user.id);
      } else {
        newSet.add(user.id);
      }
      return newSet;
    });
  };

  // Add selected users to assigned list
  const addSelectedUsersToList = () => {
    const usersToAdd = searchResults.filter(
      user => selectedUsers.has(user.id) && !assignedUsers.find(u => u.id === user.id)
    );

    if (usersToAdd.length > 0) {
      setAssignedUsers(prev => [...prev, ...usersToAdd]);
      setAssignedToUserIds(prev => [...prev, ...usersToAdd.map(u => u.id.toString())]);
      setTaskAssignments(prev => [
        ...prev,
        ...usersToAdd.map(u => ({ userId: u.id.toString(), isLeader: false })),
      ]);
    }

    closeModal();
  };

  // Clear all search filters
  const clearSearchFilters = () => {
    setSearchFilters({
      organizationId: '',
      departmentId: '',
      name: '',
    });
    setSearchResults([]);
    setSelectedUsers(new Set());
    setModalDepartments([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form
    if (!formData.taskTitle.trim()) {
      setFormError('Task title is required');
      return;
    }

    if (!formData.organizationId) {
      setFormError('Please select an organization');
      return;
    }

    if (!formData.departmentId) {
      setFormError('Please select a department');
      return;
    }

    if (assignedToUserIds.length === 0) {
      setFormError('Please select at least one assignee');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = await getToken();

      if (!token) {
        setFormError('Authentication required');
        return;
      }

      // Prepare data for API
      const payload = {
        taskTitle: formData.taskTitle.trim(),
        taskDescription: messageContent, // Use message content as description
        statusId: 1, // Auto-set to To Do status
        assignedToUserIds: assignedToUserIds.map(id => Number(id)),
        taskAssignments: taskAssignments.map(assignment => ({
          userId: parseInt(assignment.userId),
          isLeader: assignment.isLeader,
        })),
        organizationId: Number(formData.organizationId),
        departmentId: Number(formData.departmentId),
        points: formData.points ? Number(formData.points) : null,
        dueDate: formData.dueDate || null,
      };

      const response = await fetch('/api/v1/task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create task');
      }

      const result = await response.json();
      const createdTask = result.task;

      // Create task chat automatically
      try {
        const chatPayload = {
          name: `Task: ${createdTask.taskTitle}`,
          chatType: 'task',
          taskId: createdTask.id,
          organizationId: null,
          departmentId: null,
        };

        const chatResponse = await fetch('/api/v1/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(chatPayload),
        });

        if (!chatResponse.ok) {
          console.error('Failed to create task chat:', await chatResponse.json());
          // Don't throw error here - task creation was successful
          toast.success('Task created successfully from message! (Chat creation failed)');
        } else {
          console.log('Task chat created successfully');
          toast.success('Task and chat created successfully from message!');
        }
      } catch (chatError) {
        console.error('Error creating task chat:', chatError);
        // Don't throw error here - task creation was successful
        toast.success('Task created successfully from message! (Chat creation failed)');
      }

      if (onTaskCreated) {
        onTaskCreated(createdTask);
      }

      onClose();

      // Reset form
      setFormData({
        taskTitle: '',
        organizationId: '',
        departmentId: '',
        points: '',
        dueDate: '',
      });
      setAssignedUsers([]);
      setAssignedToUserIds([]);
      setTaskAssignments([]);
    } catch (error) {
      console.error('Error creating task:', error);
      setFormError(error instanceof Error ? error.message : 'Failed to create task');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Create Task from Message</ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          <MessagePreview>
            <MessagePreviewLabel>Task Description (from message):</MessagePreviewLabel>
            <MessagePreviewContent>{messageContent}</MessagePreviewContent>
          </MessagePreview>

          <FormContainer onSubmit={handleSubmit}>
            <FormGroup $fullWidth>
              <FormLabel htmlFor="taskTitle">Task Title *</FormLabel>
              <FormInput
                id="taskTitle"
                name="taskTitle"
                value={formData.taskTitle}
                onChange={handleInputChange}
                placeholder="Enter task title"
                required
              />
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="organizationId">Organization *</FormLabel>
              <FormSelect
                id="organizationId"
                name="organizationId"
                value={formData.organizationId}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Organization</option>
                {organizations.map(org => (
                  <option key={org.id} value={org.id}>
                    {org.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="departmentId">Department *</FormLabel>
              <FormSelect
                id="departmentId"
                name="departmentId"
                value={formData.departmentId}
                onChange={handleInputChange}
                required
                disabled={!formData.organizationId}
              >
                <option value="">Select Department</option>
                {departments.map(dept => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="points">Points</FormLabel>
              <FormInput
                id="points"
                name="points"
                type="number"
                min="0"
                value={formData.points}
                onChange={handleInputChange}
                placeholder="Enter story points"
              />
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="dueDate">Due Date</FormLabel>
              <FormInput
                id="dueDate"
                name="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={handleInputChange}
              />
            </FormGroup>

            <FormGroup $fullWidth>
              <FormLabel>Assigned Users</FormLabel>
              <UserListContainer>
                <UserListHeader>
                  <UserListTitle>
                    {assignedUsers.length} user{assignedUsers.length !== 1 ? 's' : ''} assigned
                  </UserListTitle>
                  <AddUserButton onClick={openModal}>
                    <Plus size={14} />
                    Add User
                  </AddUserButton>
                </UserListHeader>
                <UserListContent>
                  {assignedUsers.length === 0 ? (
                    <EmptyUserList>No users assigned yet</EmptyUserList>
                  ) : (
                    assignedUsers.map(user => (
                      <UserItem key={user.id}>
                        <UserAvatar $imageUrl={user.imageUrl}>
                          {!user.imageUrl && getUserInitials(user.name)}
                        </UserAvatar>
                        <UserInfo>
                          <UserName>
                            {user.name}
                            {user.isOwner && (
                              <OwnerBadge>Owner</OwnerBadge>
                            )}
                            {user.isAdmin && (
                              <AdminBadge>Admin</AdminBadge>
                            )}
                            {user.isLeader && (
                              <LeaderBadge>Leader</LeaderBadge>
                            )}
                          </UserName>
                          <UserEmail>{user.email}</UserEmail>
                          {(user.departmentName || user.organizationName) && (
                            <UserDepartmentOrg>
                              {user.departmentName && user.organizationName
                                ? `${user.departmentName} • ${user.organizationName}`
                                : user.departmentName || user.organizationName}
                            </UserDepartmentOrg>
                          )}
                        </UserInfo>
                        <UserActions>
                          <LeaderCheckboxLabel>
                            <CustomCheckbox
                              checked={
                                taskAssignments.find(
                                  assignment => assignment.userId === user.id.toString()
                                )?.isLeader || false
                              }
                              onChange={() => toggleLeaderStatus(user.id.toString())}
                            />
                            <LeaderText>Leader</LeaderText>
                          </LeaderCheckboxLabel>
                          <RemoveUserButton onClick={() => removeUserFromList(user.id)}>
                            ×
                          </RemoveUserButton>
                        </UserActions>
                      </UserItem>
                    ))
                  )}
                </UserListContent>
              </UserListContainer>
            </FormGroup>

            {formError && <ErrorMessage>{formError}</ErrorMessage>}

            <FormActions>
              <Button type="button" $variant="secondary" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" $variant="primary" disabled={isSubmitting}>
                {isSubmitting && <LoadingSpinner size={16} />}
                {isSubmitting ? 'Creating...' : 'Create Task'}
              </Button>
            </FormActions>
          </FormContainer>
        </ModalBody>
      </ModalContent>
    </ModalOverlay>

    {isModalOpen && (
      <SearchModalOverlay onClick={closeModal}>
        <SearchModalContent onClick={e => e.stopPropagation()}>
          <SearchModalHeader>
            <SearchModalTitle>Find Users</SearchModalTitle>
            <SearchCloseButton onClick={closeModal}>
              <X size={20} />
            </SearchCloseButton>
          </SearchModalHeader>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', marginBottom: '12px' }}>
            <FormGroup>
              <FormLabel>Organization</FormLabel>
              <FormSelect
                value={searchFilters.organizationId}
                onChange={e => handleSearchFilterChange('organizationId', e.target.value)}
              >
                <option value="">All Organizations</option>
                {modalOrganizations.map(org => (
                  <option key={org.id} value={org.id}>
                    {org.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>

            <FormGroup>
              <FormLabel>Department</FormLabel>
              <FormSelect
                value={searchFilters.departmentId}
                onChange={e => handleSearchFilterChange('departmentId', e.target.value)}
                disabled={!searchFilters.organizationId}
              >
                <option value="">
                  {searchFilters.organizationId ? 'All Departments' : 'Select organization first'}
                </option>
                {modalDepartments.map(dept => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>

            <FormGroup style={{ gridColumn: '1 / -1' }}>
              <FormLabel>Name</FormLabel>
              <div style={{ position: 'relative' }}>
                <div style={{ position: 'absolute', left: '12px', top: '50%', transform: 'translateY(-50%)', color: '#6b7280' }}>
                  <Search size={16} />
                </div>
                <FormInput
                  type="text"
                  placeholder="Search by name or email..."
                  value={searchFilters.name}
                  onChange={e => handleSearchFilterChange('name', e.target.value)}
                  style={{ paddingLeft: '36px' }}
                />
              </div>
            </FormGroup>
          </div>

          {(searchFilters.organizationId || searchFilters.departmentId || searchFilters.name.trim()) && (
            <>
              <button
                onClick={clearSearchFilters}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  padding: '4px 8px',
                  background: 'rgba(239, 68, 68, 0.1)',
                  color: '#ef4444',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  marginBottom: '8px'
                }}
              >
                <X size={14} />
                Clear Filters
              </button>

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                padding: '4px 8px',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                border: '1px solid rgba(59, 130, 246, 0.3)',
                borderRadius: '4px',
                marginBottom: '8px',
                fontSize: '12px',
                color: '#1d4ed8',
                fontWeight: 500
              }}>
                <Filter size={14} />
                <span>
                  Filtering by:{' '}
                  {searchFilters.organizationId && (
                    <span>
                      {modalOrganizations.find(
                        org => org.id.toString() === searchFilters.organizationId
                      )?.name || 'Organization'}
                      {(searchFilters.departmentId || searchFilters.name.trim()) && ', '}
                    </span>
                  )}
                  {searchFilters.departmentId && (
                    <span>
                      {modalDepartments.find(
                        dept => dept.id.toString() === searchFilters.departmentId
                      )?.name || 'Department'}
                      {searchFilters.name.trim() && ', '}
                    </span>
                  )}
                  {searchFilters.name.trim() && <span>Name: "{searchFilters.name}"</span>}
                </span>
              </div>
            </>
          )}

          {isSearching && (
            <div style={{
              textAlign: 'center',
              padding: '8px',
              color: '#6b7280',
              fontSize: '14px'
            }}>
              <LoadingSpinner size={14} style={{ marginRight: '4px' }} />
              Searching...
            </div>
          )}

          {searchResults.length > 0 && (
            <>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '8px',
                padding: '4px 8px',
                backgroundColor: '#f8fafc',
                borderRadius: '4px'
              }}>
                <span style={{ fontSize: '12px', color: '#6b7280' }}>
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                </span>
                <button
                  onClick={addSelectedUsersToList}
                  disabled={selectedUsers.size === 0}
                  style={{
                    backgroundColor: selectedUsers.size === 0 ? '#9ca3af' : '#10b981',
                    color: 'white',
                    border: 'none',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    cursor: selectedUsers.size === 0 ? 'not-allowed' : 'pointer'
                  }}
                >
                  Add Selected ({selectedUsers.size})
                </button>
              </div>

              <div style={{
                maxHeight: '280px',
                overflowY: 'auto',
                border: '1px solid #e5e7eb',
                borderRadius: '4px'
              }}>
                {searchResults.map(user => (
                  <div
                    key={user.id}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '8px',
                      borderBottom: '1px solid #f3f4f6'
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <CustomCheckbox
                        checked={selectedUsers.has(user.id)}
                        onChange={() => toggleUserSelection(user)}
                      />
                      <UserAvatar $imageUrl={user.imageUrl} $size="small">
                        {!user.imageUrl && getUserInitials(user.name)}
                      </UserAvatar>
                      <div>
                        <div style={{
                          fontWeight: 500,
                          fontSize: '14px',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          flexWrap: 'wrap'
                        }}>
                          {user.name}
                          {user.isOwner && (
                            <OwnerBadge>Owner</OwnerBadge>
                          )}
                          {user.isAdmin && (
                            <AdminBadge>Admin</AdminBadge>
                          )}
                          {user.isLeader && (
                            <LeaderBadge>Leader</LeaderBadge>
                          )}
                        </div>
                        <div style={{ fontSize: '12px', color: '#6b7280' }}>{user.email}</div>
                        {(user.departmentName || user.organizationName) && (
                          <div style={{ fontSize: '11px', color: '#9ca3af', fontStyle: 'italic' }}>
                            {user.departmentName && user.organizationName
                              ? `${user.departmentName} • ${user.organizationName}`
                              : user.departmentName || user.organizationName}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}

          {!isSearching && searchResults.length === 0 && (searchFilters.organizationId || searchFilters.departmentId || searchFilters.name.trim()) && (
            <div style={{
              textAlign: 'center',
              padding: '16px',
              color: '#6b7280',
              fontSize: '14px',
              backgroundColor: '#f9fafb',
              borderRadius: '4px',
              border: '1px dashed #d1d5db'
            }}>
              No users found matching your search criteria
            </div>
          )}
        </SearchModalContent>
      </SearchModalOverlay>
    )}
    </>
  );
}
